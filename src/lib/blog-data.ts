// Mock data cho blog posts
export interface BlogPost {
  slug: string;
  title: string;
  content: string;
  excerpt: string;
  publishedAt: string;
  author: string;
  tags: string[];
}

export const blogPosts: BlogPost[] = [
  {
    slug: "gioi-thieu-nextjs-15",
    title: "Giới thiệu Next.js 15 - Những tính năng mới",
    content: `
      <h2>Next.js 15 đã ra mắt với nhiều tính năng mới thú vị</h2>
      <p>Next.js 15 mang đến nhiều cải tiến quan trọng cho developers:</p>
      <ul>
        <li><strong>React 19 Support:</strong> Hỗ trợ đầy đủ React 19 với các tính năng mới</li>
        <li><strong>Turbopack:</strong> Bundler mới nhanh hơn Webpack</li>
        <li><strong>Caching Improvements:</strong> C<PERSON>i thiện caching mechanism</li>
        <li><strong>Server Components:</strong> T<PERSON>i <PERSON>u hóa Server Components</li>
      </ul>
      <p>Những cải tiến này giúp ứng dụng Next.js chạy nhanh hơn và developer experience tốt hơn.</p>
    `,
    excerpt: "Khám phá những tính năng mới và cải tiến trong Next.js 15",
    publishedAt: "2024-01-15",
    author: "Nguyễn Văn A",
    tags: ["Next.js", "React", "Web Development"]
  },
  {
    slug: "hoc-react-hooks",
    title: "Học React Hooks từ cơ bản đến nâng cao",
    content: `
      <h2>React Hooks - Cách tiếp cận hiện đại cho React</h2>
      <p>React Hooks đã thay đổi cách chúng ta viết React components:</p>
      <h3>Hooks cơ bản:</h3>
      <ul>
        <li><strong>useState:</strong> Quản lý state trong functional components</li>
        <li><strong>useEffect:</strong> Xử lý side effects</li>
        <li><strong>useContext:</strong> Sử dụng React Context</li>
      </ul>
      <h3>Hooks nâng cao:</h3>
      <ul>
        <li><strong>useReducer:</strong> Quản lý state phức tạp</li>
        <li><strong>useMemo:</strong> Tối ưu hóa performance</li>
        <li><strong>useCallback:</strong> Memoize functions</li>
      </ul>
      <p>Việc hiểu và sử dụng thành thạo Hooks sẽ giúp bạn viết code React hiệu quả hơn.</p>
    `,
    excerpt: "Hướng dẫn chi tiết về React Hooks từ cơ bản đến nâng cao",
    publishedAt: "2024-01-10",
    author: "Trần Thị B",
    tags: ["React", "Hooks", "JavaScript"]
  },
  {
    slug: "tailwind-css-tips",
    title: "10 Tips sử dụng Tailwind CSS hiệu quả",
    content: `
      <h2>Tailwind CSS - Framework CSS utility-first</h2>
      <p>Dưới đây là 10 tips giúp bạn sử dụng Tailwind CSS hiệu quả:</p>
      <ol>
        <li><strong>Sử dụng @apply directive:</strong> Tạo custom classes</li>
        <li><strong>Responsive design:</strong> Sử dụng breakpoint prefixes</li>
        <li><strong>Dark mode:</strong> Implement dark mode dễ dàng</li>
        <li><strong>Custom colors:</strong> Tùy chỉnh color palette</li>
        <li><strong>Spacing system:</strong> Hiểu hệ thống spacing</li>
        <li><strong>Component extraction:</strong> Tách component để tái sử dụng</li>
        <li><strong>Purge CSS:</strong> Loại bỏ CSS không sử dụng</li>
        <li><strong>JIT mode:</strong> Sử dụng Just-in-Time compilation</li>
        <li><strong>Plugins:</strong> Mở rộng với plugins</li>
        <li><strong>Best practices:</strong> Tuân thủ best practices</li>
      </ol>
      <p>Tailwind CSS giúp bạn xây dựng UI nhanh chóng và maintainable.</p>
    `,
    excerpt: "Những tips và tricks để sử dụng Tailwind CSS một cách hiệu quả",
    publishedAt: "2024-01-05",
    author: "Lê Văn C",
    tags: ["Tailwind CSS", "CSS", "UI/UX"]
  },
  {
    slug: "typescript-best-practices",
    title: "TypeScript Best Practices cho dự án lớn",
    content: `
      <h2>TypeScript Best Practices</h2>
      <p>Khi làm việc với TypeScript trong dự án lớn, cần tuân thủ các best practices:</p>
      <h3>Type Safety:</h3>
      <ul>
        <li>Sử dụng strict mode</li>
        <li>Tránh sử dụng 'any' type</li>
        <li>Định nghĩa interfaces rõ ràng</li>
      </ul>
      <h3>Code Organization:</h3>
      <ul>
        <li>Tổ chức types và interfaces</li>
        <li>Sử dụng barrel exports</li>
        <li>Tách biệt business logic</li>
      </ul>
      <h3>Performance:</h3>
      <ul>
        <li>Sử dụng type assertions cẩn thận</li>
        <li>Optimize compilation time</li>
        <li>Lazy loading types</li>
      </ul>
      <p>Việc tuân thủ các best practices này sẽ giúp dự án TypeScript dễ maintain và scale.</p>
    `,
    excerpt: "Hướng dẫn best practices khi sử dụng TypeScript trong dự án lớn",
    publishedAt: "2024-01-01",
    author: "Phạm Văn D",
    tags: ["TypeScript", "Best Practices", "Programming"]
  }
];

// Helper functions
export function getAllPosts(): BlogPost[] {
  return blogPosts.sort((a, b) => 
    new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
  );
}

export function getPostBySlug(slug: string): BlogPost | undefined {
  return blogPosts.find(post => post.slug === slug);
}

export function getAllSlugs(): string[] {
  return blogPosts.map(post => post.slug);
}
