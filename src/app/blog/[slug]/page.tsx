import Link from 'next/link';
import { notFound } from 'next/navigation';
import { getAllSlugs, getPostBySlug } from '@/lib/blog-data';

// ✨ Đây là function generateStaticParams - tính năng chính của ví dụ này
export async function generateStaticParams() {
  // Lấy tất cả các slug từ blog posts
  const slugs = getAllSlugs();
  
  // Trả về array các object chứa params cho dynamic route
  // Mỗi object sẽ tương ứng với một trang được pre-render tại build time
  return slugs.map((slug) => ({
    slug: slug, // Tương ứng với [slug] trong tên file
  }));
}

// Interface cho props của component
interface PageProps {
  params: Promise<{ slug: string }>;
}

export default async function BlogPostPage({ params }: PageProps) {
  // Await params để lấy slug từ URL
  const { slug } = await params;
  
  // Tì<PERSON> bài viết theo slug
  const post = getPostBySlug(slug);

  // Nếu không tìm thấy bài viết, hiển thị 404
  if (!post) {
    notFound();
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Navigation */}
      <nav className="mb-8">
        <Link 
          href="/blog"
          className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
        >
          <svg 
            className="mr-2 w-4 h-4" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M15 19l-7-7 7-7" 
            />
          </svg>
          Quay lại Blog
        </Link>
      </nav>

      {/* Article Header */}
      <header className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          {post.title}
        </h1>
        
        <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0 text-gray-600 dark:text-gray-400">
          <time dateTime={post.publishedAt}>
            {new Date(post.publishedAt).toLocaleDateString('vi-VN', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </time>
          <span className="hidden sm:block">•</span>
          <span>Bởi {post.author}</span>
        </div>

        <div className="flex flex-wrap gap-2 mt-4">
          {post.tags.map((tag) => (
            <span 
              key={tag}
              className="inline-block bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm px-3 py-1 rounded-full"
            >
              {tag}
            </span>
          ))}
        </div>
      </header>

      {/* Article Content */}
      <article className="prose prose-lg dark:prose-invert max-w-none">
        <div 
          className="text-gray-800 dark:text-gray-200 leading-relaxed"
          dangerouslySetInnerHTML={{ __html: post.content }}
        />
      </article>

      {/* Footer */}
      <footer className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <Link 
            href="/blog"
            className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
          >
            <svg 
              className="mr-2 w-4 h-4" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M15 19l-7-7 7-7" 
              />
            </svg>
            Xem tất cả bài viết
          </Link>
          
          <Link 
            href="/"
            className="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
          >
            Về trang chủ
          </Link>
        </div>
      </footer>
    </div>
  );
}

// Tạo metadata động cho SEO
export async function generateMetadata({ params }: PageProps) {
  const { slug } = await params;
  const post = getPostBySlug(slug);

  if (!post) {
    return {
      title: 'Bài viết không tìm thấy',
    };
  }

  return {
    title: post.title,
    description: post.excerpt,
  };
}
