import Image from 'next/image'

export default function RemoteImageDemo() {
	const remoteImageUrl = 'https://pplx-res.cloudinary.com/image/upload/v1749386041/gpt4o_images/dbvrk4w8mmuolimr04ew.png'

	return (
		<div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4">
			<div className="max-w-4xl mx-auto">
				<div className="text-center mb-12">
					<h1 className="text-4xl font-bold text-gray-900 mb-4">
						Remote Image Demo với NextJS
					</h1>
					<p className="text-lg text-gray-600 max-w-2xl mx-auto">
						Ví dụ về cách sử dụng Next.js Image component để hiển thị ảnh từ URL remote
						với các best practices và tối ưu hóa tự động.
					</p>
				</div>

				{/* Basic Remote Image */}
				<div className="bg-white rounded-xl shadow-lg p-8 mb-8">
					<h2 className="text-2xl font-semibold text-gray-800 mb-6">
						1. Basic Remote Image
					</h2>
					<div className="flex flex-col lg:flex-row gap-8 items-center">
						<div className="flex-1">
							<div className="relative">
								<Image
									src={remoteImageUrl}
									alt="Remote image example - AI generated content"
									width={400}
									height={300}
									className="rounded-lg shadow-md"
									priority
								/>
							</div>
						</div>
						<div className="flex-1">
							<h3 className="text-lg font-medium text-gray-700 mb-4">
								Các điểm quan trọng:
							</h3>
							<ul className="space-y-3 text-gray-600">
								<li className="flex items-start">
									<span className="text-green-500 mr-2">✓</span>
									<span><strong>width & height:</strong> Bắt buộc cho remote images</span>
								</li>
								<li className="flex items-start">
									<span className="text-green-500 mr-2">✓</span>
									<span><strong>alt text:</strong> Mô tả chi tiết cho accessibility</span>
								</li>
								<li className="flex items-start">
									<span className="text-green-500 mr-2">✓</span>
									<span><strong>priority:</strong> Load ngay lập tức cho above-the-fold</span>
								</li>
								<li className="flex items-start">
									<span className="text-green-500 mr-2">✓</span>
									<span><strong>className:</strong> Styling với Tailwind CSS</span>
								</li>
							</ul>
						</div>
					</div>
				</div>

				{/* Responsive Remote Image */}
				<div className="bg-white rounded-xl shadow-lg p-8 mb-8">
					<h2 className="text-2xl font-semibold text-gray-800 mb-6">
						2. Responsive Remote Image
					</h2>
					<div className="mb-6">
						<Image
							src={remoteImageUrl}
							alt="Responsive remote image example"
							width={800}
							height={600}
							sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
							style={{
								width: '100%',
								height: 'auto',
							}}
							className="rounded-lg shadow-md"
						/>
					</div>
					<div className="text-gray-600">
						<p className="mb-2">
							<strong>sizes prop:</strong> Định nghĩa kích thước responsive cho các breakpoint khác nhau
						</p>
						<code className="bg-gray-100 px-2 py-1 rounded text-sm">
							sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
						</code>
					</div>
				</div>

				{/* Image with Blur Placeholder */}
				<div className="bg-white rounded-xl shadow-lg p-8 mb-8">
					<h2 className="text-2xl font-semibold text-gray-800 mb-6">
						3. Image với Blur Placeholder
					</h2>
					<div className="flex flex-col lg:flex-row gap-8 items-center">
						<div className="flex-1">
							<Image
								src={remoteImageUrl}
								alt="Remote image with blur placeholder"
								width={350}
								height={250}
								placeholder="blur"
								blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
								className="rounded-lg shadow-md"
							/>
						</div>
						<div className="flex-1">
							<h3 className="text-lg font-medium text-gray-700 mb-4">
								Blur Placeholder:
							</h3>
							<ul className="space-y-3 text-gray-600">
								<li className="flex items-start">
									<span className="text-blue-500 mr-2">→</span>
									<span><strong>placeholder="blur":</strong> Hiển thị blur effect khi loading</span>
								</li>
								<li className="flex items-start">
									<span className="text-blue-500 mr-2">→</span>
									<span><strong>blurDataURL:</strong> Base64 image nhỏ làm placeholder</span>
								</li>
								<li className="flex items-start">
									<span className="text-blue-500 mr-2">→</span>
									<span>Cải thiện perceived performance</span>
								</li>
							</ul>
						</div>
					</div>
				</div>

				{/* Multiple Quality Examples */}
				<div className="bg-white rounded-xl shadow-lg p-8 mb-8">
					<h2 className="text-2xl font-semibold text-gray-800 mb-6">
						4. Different Quality Settings
					</h2>
					<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
						<div className="text-center">
							<Image
								src={remoteImageUrl}
								alt="Low quality image"
								width={200}
								height={150}
								quality={25}
								className="rounded-lg shadow-sm"
							/>
							<p className="mt-2 text-sm text-gray-600">Quality: 25</p>
						</div>
						<div className="text-center">
							<Image
								src={remoteImageUrl}
								alt="Medium quality image"
								width={200}
								height={150}
								quality={75}
								className="rounded-lg shadow-sm"
							/>
							<p className="mt-2 text-sm text-gray-600">Quality: 75 (default)</p>
						</div>
						<div className="text-center">
							<Image
								src={remoteImageUrl}
								alt="High quality image"
								width={200}
								height={150}
								quality={95}
								className="rounded-lg shadow-sm"
							/>
							<p className="mt-2 text-sm text-gray-600">Quality: 95</p>
						</div>
					</div>
				</div>

				{/* Code Examples */}
				<div className="bg-gray-900 rounded-xl shadow-lg p-8 text-white">
					<h2 className="text-2xl font-semibold mb-6">
						5. Code Examples
					</h2>
					<div className="space-y-6">
						<div>
							<h3 className="text-lg font-medium text-green-400 mb-3">
								Basic Remote Image:
							</h3>
							<pre className="bg-gray-800 p-4 rounded-lg overflow-x-auto text-sm">
								<code>{`import Image from 'next/image'

export default function MyComponent() {
  return (
    <Image
      src="${remoteImageUrl}"
      alt="Remote image description"
      width={400}
      height={300}
      priority
    />
  )
}`}</code>
							</pre>
						</div>

						<div>
							<h3 className="text-lg font-medium text-green-400 mb-3">
								Next.config.ts Configuration:
							</h3>
							<pre className="bg-gray-800 p-4 rounded-lg overflow-x-auto text-sm">
								<code>{`const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'pplx-res.cloudinary.com',
        port: '',
        pathname: '/image/upload/**',
      },
    ],
  },
}`}</code>
							</pre>
						</div>
					</div>
				</div>

				{/* Best Practices */}
				<div className="bg-yellow-50 border border-yellow-200 rounded-xl p-8 mt-8">
					<h2 className="text-2xl font-semibold text-yellow-800 mb-6">
						💡 Best Practices cho Remote Images
					</h2>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<div>
							<h3 className="text-lg font-medium text-yellow-700 mb-3">
								Bắt buộc:
							</h3>
							<ul className="space-y-2 text-yellow-700">
								<li>• Luôn cung cấp width & height</li>
								<li>• Cấu hình remotePatterns trong next.config</li>
								<li>• Sử dụng alt text mô tả chi tiết</li>
								<li>• Kiểm tra CORS policy của domain</li>
							</ul>
						</div>
						<div>
							<h3 className="text-lg font-medium text-yellow-700 mb-3">
								Khuyến nghị:
							</h3>
							<ul className="space-y-2 text-yellow-700">
								<li>• Sử dụng priority cho above-fold images</li>
								<li>• Thêm blur placeholder cho UX tốt hơn</li>
								<li>• Cấu hình sizes cho responsive</li>
								<li>• Tối ưu quality dựa trên use case</li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div>
	)
} 