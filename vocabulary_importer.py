#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vocabulary Tracker - Import từ vựng nghề nghiệp vào Notion
Tác giả: <PERSON><PERSON><PERSON><PERSON> viên tiếng <PERSON>
"""

import re
import json
from datetime import datetime
from typing import List, Dict, Tuple

class VocabularyImporter:
    def __init__(self):
        self.categories = {
            "1.1": {"name": "🏛️ Law & Security", "difficulty": "Intermediate"},
            "1.2": {"name": "💻 IT & Engineering", "difficulty": "Advanced"},
            "1.3": {"name": "💰 Finance & Business", "difficulty": "Intermediate"},
            "1.4": {"name": "🏥 Healthcare & Social Work", "difficulty": "Intermediate"},
            "1.5": {"name": "🔬 Science", "difficulty": "Advanced"},
            "1.6": {"name": "🎭 Arts & Entertainment", "difficulty": "Beginner"},
            "1.7": {"name": "🛍️ Retail", "difficulty": "Beginner"},
            "1.8": {"name": "📋 Administration", "difficulty": "Intermediate"},
            "1.9": {"name": "🏨 Travel & Hospitality", "difficulty": "Beginner"},
            "1.10": {"name": "🔬 Science", "difficulty": "Advanced"},
            "1.11": {"name": "🚗 Transportation", "difficulty": "Beginner"},
            "1.12": {"name": "🎭 Arts & Entertainment", "difficulty": "Beginner"},
            "1.13": {"name": "📚 Education", "difficulty": "Intermediate"},
            "1.14": {"name": "⛪ Religion", "difficulty": "Intermediate"},
            "1.15": {"name": "🪖 Military & Security", "difficulty": "Advanced"},
            "1.16": {"name": "⚙️ Other Professions", "difficulty": "Beginner"}
        }
        
        self.vocabulary_data = []
    
    def parse_vocabulary_file(self, file_path: str) -> List[Dict]:
        """Đọc và phân tích file từ vựng"""
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        current_category = ""
        current_section = ""
        
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # Tìm header section (## 1.X Lĩnh vực...)
            section_match = re.match(r'^## (1\.\d+) (.+)$', line)
            if section_match:
                current_section = section_match.group(1)
                current_category = self.categories.get(current_section, {}).get("name", "⚙️ Other")
                continue
            
            # Tìm từ vựng (**Word** (pronunciation): meaning)
            vocab_match = re.match(r'^\*\*(.+?)\*\*(?:\s*\((.+?)\))?\s*:\s*(.+)$', line)
            if vocab_match and current_category:
                word = vocab_match.group(1).strip()
                pronunciation = vocab_match.group(2) if vocab_match.group(2) else ""
                meaning = vocab_match.group(3).strip()
                
                # Xác định độ khó dựa trên category
                difficulty = self.categories.get(current_section, {}).get("difficulty", "Beginner")
                
                # Tạo câu ví dụ đơn giản
                example = self.generate_example_sentence(word, meaning)
                
                vocab_entry = {
                    "word": word,
                    "vietnamese_meaning": meaning,
                    "pronunciation": pronunciation,
                    "category": current_category,
                    "difficulty_level": difficulty,
                    "learning_status": "New",
                    "date_added": datetime.now().strftime("%Y-%m-%d"),
                    "priority": "Medium",
                    "example_sentence": example,
                    "notes": f"Thuộc lĩnh vực: {current_category.split(' ', 1)[1] if ' ' in current_category else current_category}"
                }
                
                self.vocabulary_data.append(vocab_entry)
        
        return self.vocabulary_data
    
    def generate_example_sentence(self, word: str, meaning: str) -> str:
        """Tạo câu ví dụ đơn giản cho từ vựng"""
        examples = {
            # Nghề nghiệp cơ bản
            "doctor": "The doctor examined the patient carefully.",
            "teacher": "My teacher explains the lesson very well.",
            "engineer": "The engineer designed a new bridge.",
            "lawyer": "The lawyer defended his client in court.",
            "nurse": "The nurse took care of the patients.",
            "chef": "The chef prepared a delicious meal.",
            "pilot": "The pilot flew the plane safely.",
            "artist": "The artist painted a beautiful picture.",
        }
        
        # Nếu có ví dụ có sẵn, dùng luôn
        word_lower = word.lower()
        if word_lower in examples:
            return examples[word_lower]
        
        # Tạo câu ví dụ chung
        if "manager" in word_lower:
            return f"The {word.lower()} leads the team effectively."
        elif "officer" in word_lower:
            return f"The {word.lower()} performs their duties responsibly."
        elif "assistant" in word_lower:
            return f"The {word.lower()} helps with daily tasks."
        elif "designer" in word_lower:
            return f"The {word.lower()} creates innovative designs."
        elif "technician" in word_lower:
            return f"The {word.lower()} maintains the equipment."
        else:
            return f"I want to become a {word.lower()} in the future."
    
    def export_to_json(self, output_file: str = "vocabulary_data.json"):
        """Xuất dữ liệu ra file JSON"""
        with open(output_file, 'w', encoding='utf-8') as file:
            json.dump(self.vocabulary_data, file, ensure_ascii=False, indent=2)
        
        print(f"✅ Đã xuất {len(self.vocabulary_data)} từ vựng ra file {output_file}")
    
    def create_notion_payload(self) -> List[Dict]:
        """Tạo payload cho Notion API"""
        notion_entries = []
        
        for vocab in self.vocabulary_data:
            notion_entry = {
                "parent": {"database_id": "YOUR_DATABASE_ID"},  # Thay bằng ID database thực
                "properties": {
                    "Word": {
                        "title": [{"text": {"content": vocab["word"]}}]
                    },
                    "Vietnamese Meaning": {
                        "rich_text": [{"text": {"content": vocab["vietnamese_meaning"]}}]
                    },
                    "Pronunciation": {
                        "rich_text": [{"text": {"content": vocab["pronunciation"]}}]
                    },
                    "Category": {
                        "select": {"name": vocab["category"]}
                    },
                    "Difficulty Level": {
                        "select": {"name": vocab["difficulty_level"]}
                    },
                    "Learning Status": {
                        "select": {"name": vocab["learning_status"]}
                    },
                    "Date Added": {
                        "date": {"start": vocab["date_added"]}
                    },
                    "Priority": {
                        "select": {"name": vocab["priority"]}
                    },
                    "Example Sentence": {
                        "rich_text": [{"text": {"content": vocab["example_sentence"]}}]
                    },
                    "Notes": {
                        "rich_text": [{"text": {"content": vocab["notes"]}}]
                    }
                }
            }
            notion_entries.append(notion_entry)
        
        return notion_entries
    
    def print_statistics(self):
        """In thống kê từ vựng"""
        print("\n📊 THỐNG KÊ TỪ VỰNG:")
        print(f"Tổng số từ vựng: {len(self.vocabulary_data)}")
        
        # Thống kê theo category
        category_count = {}
        difficulty_count = {}
        
        for vocab in self.vocabulary_data:
            category = vocab["category"]
            difficulty = vocab["difficulty_level"]
            
            category_count[category] = category_count.get(category, 0) + 1
            difficulty_count[difficulty] = difficulty_count.get(difficulty, 0) + 1
        
        print("\n📂 Theo lĩnh vực:")
        for category, count in sorted(category_count.items()):
            print(f"  {category}: {count} từ")
        
        print("\n📈 Theo độ khó:")
        for difficulty, count in sorted(difficulty_count.items()):
            print(f"  {difficulty}: {count} từ")

def main():
    """Hàm chính"""
    print("🎓 VOCABULARY TRACKER - IMPORT TỪ VỰNG NGHỀ NGHIỆP")
    print("=" * 60)
    
    importer = VocabularyImporter()
    
    # Đọc file từ vựng
    try:
        vocabulary_data = importer.parse_vocabulary_file("chu_de_nghe_nghiep.md")
        print(f"✅ Đã đọc thành công {len(vocabulary_data)} từ vựng")
        
        # In thống kê
        importer.print_statistics()
        
        # Xuất ra JSON
        importer.export_to_json()
        
        # Tạo payload cho Notion (để sử dụng sau)
        notion_payload = importer.create_notion_payload()
        with open("notion_payload.json", 'w', encoding='utf-8') as file:
            json.dump(notion_payload, file, ensure_ascii=False, indent=2)
        
        print(f"\n✅ Đã tạo payload cho Notion API: notion_payload.json")
        print("\n📝 Bước tiếp theo:")
        print("1. Tạo database trong Notion với cấu trúc đã thiết kế")
        print("2. Lấy Database ID từ Notion")
        print("3. Thay thế 'YOUR_DATABASE_ID' trong notion_payload.json")
        print("4. Sử dụng Notion API để import dữ liệu")
        
    except FileNotFoundError:
        print("❌ Không tìm thấy file chu_de_nghe_nghiep.md")
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
