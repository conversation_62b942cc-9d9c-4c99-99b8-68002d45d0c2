# Workflow: Remote Image Example

## Current tasks from user prompt:
- Tạo ví dụ về image remote trong NextJS
- Sử dụng URL image: https://pplx-res.cloudinary.com/image/upload/v1749386041/gpt4o_images/dbvrk4w8mmuolimr04ew.png
- Sử dụng Context7 để lấy docs mới nhất về thẻ image trong NextJS

## Plan (simple):
1. Sử dụng Context7 để lấy documentation về NextJS Image component
2. Tạo một trang ví dụ sử dụng remote image với URL được cung cấp
3. Thê<PERSON> các best practices cho remote images như width, height, alt text
4. C<PERSON>u hình next.config.js để cho phép external images nếu cần

## Steps:
1. ✅ Lấy documentation từ Context7 về NextJS Image component
2. ✅ Kiểm tra cấu trúc project hiện tại
3. ✅ Tạo component hoặc page để demo remote image
4. ✅ <PERSON><PERSON><PERSON> hình next.config.js nếu cần cho external domains
5. ✅ Test và hoàn thiện ví dụ

## Things done:
- ✅ Đã lấy documentation từ Context7 về NextJS Image component
- ✅ Tạo file memory bank để tracking workflow
- ✅ Cấu hình next.config.ts với remotePatterns cho domain pplx-res.cloudinary.com
- ✅ Tạo page demo hoàn chỉnh tại `/remote-image-demo` với nhiều ví dụ:
  - Basic remote image với priority
  - Responsive image với sizes prop
  - Image với blur placeholder
  - So sánh các mức quality khác nhau
  - Code examples và best practices
- ✅ Thêm navigation link từ homepage đến demo page
- ✅ Apply best practices theo documentation Context7

## Things aren't done yet:
- Chạy thử project để kiểm tra hoạt động 